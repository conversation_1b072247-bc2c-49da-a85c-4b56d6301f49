// uniCloud云函数：统一任务调度器
"use strict";

/**
 * 统一任务调度器 - 负责检测和推进所有视频处理任务
 * 
 * 核心功能：
 * 1. 定时扫描所有进行中的任务
 * 2. 根据任务状态自动推进到下一阶段
 * 3. 处理异常和失败情况
 * 
 * @param {Object} event
 * @param {string} event.taskId - 可选，指定处理的任务ID
 * @param {string} event.action - 可选，指定执行的动作
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log("task-scheduler 云函数被调用，参数：", event);

    const { taskId, action } = event;
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 如果指定了taskId，只处理该任务
    if (taskId) {
      const result = await processSingleTask(taskId, tasksCollection);
      return {
        code: 200,
        message: "单个任务处理完成",
        data: result
      };
    }

    // 如果指定了action，执行特定动作
    if (action === 'check_all') {
      const result = await checkAllTasks(tasksCollection);
      return {
        code: 200,
        message: "批量检查完成",
        data: result
      };
    }

    // 默认：扫描所有需要处理的任务
    const result = await scanAndProcessTasks(tasksCollection);

    return {
      code: 200,
      message: "任务调度完成",
      data: result
    };

  } catch (error) {
    console.error("task-scheduler 云函数执行错误：", error);
    return {
      code: 500,
      message: "任务调度失败: " + error.message,
    };
  }
};

/**
 * 扫描并处理所有需要处理的任务
 */
async function scanAndProcessTasks(tasksCollection) {
  console.log("开始扫描所有需要处理的任务");
  const db = uniCloud.database();
  // 查询所有进行中的任务（不包括completed、failed、cancelled）
  const activeStatuses = ['extracting_audio', 'recognizing', 'translating', 'merging'];
  
  const tasksResult = await tasksCollection
    .where({
      status: db.command.in(activeStatuses)
    })
    .orderBy('updateTime', 'asc') // 按更新时间排序，优先处理较早的任务
    .get();

  const tasks = tasksResult.data || [];
  console.log(`找到 ${tasks.length} 个需要处理的任务`);

  if (tasks.length === 0) {
    return {
      processedCount: 0,
      results: []
    };
  }

  // 处理每个任务
  const results = [];
  for (const task of tasks) {
    try {
      const result = await processTaskByStatus(task, tasksCollection);
      results.push({
        taskId: task._id,
        success: true,
        result: result
      });
    } catch (error) {
      console.error(`处理任务 ${task._id} 失败：`, error);
      results.push({
        taskId: task._id,
        success: false,
        error: error.message
      });
    }
  }

  return {
    processedCount: tasks.length,
    results: results
  };
}

/**
 * 处理单个指定的任务
 */
async function processSingleTask(taskId, tasksCollection) {
  console.log(`处理指定任务：${taskId}`);

  const taskResult = await tasksCollection.doc(taskId).get();
  if (!taskResult.data || taskResult.data.length === 0) {
    throw new Error("任务不存在");
  }

  const task = taskResult.data[0];
  return await processTaskByStatus(task, tasksCollection);
}

/**
 * 根据任务状态处理任务
 */
async function processTaskByStatus(task, tasksCollection) {
  const { _id: taskId, status } = task;
  console.log(`处理任务 ${taskId}，当前状态：${status}`);

  switch (status) {
    case 'uploading':
      // 上传状态通常由前端或其他服务处理，这里只检查是否超时
      return await checkUploadTimeout(task, tasksCollection);

    case 'extracting_audio':
      // 检查MPS任务状态
      return await checkMpsJobStatus(task, tasksCollection);

    case 'recognizing':
      // 检查语音识别状态
      return await checkRecognitionStatus(task, tasksCollection);

    case 'translating':
      // 检查翻译状态，翻译由 subtitle-translation-gpt 云函数处理
      return await checkTranslationStatus(task, tasksCollection);

    case 'merging':
      // 检查视频合并状态
      return await checkMergingStatus(task, tasksCollection);

    default:
      console.log(`任务 ${taskId} 状态 ${status} 无需处理`);
      return { action: 'no_action', status: status };
  }
}

/**
 * 检查上传超时
 */
async function checkUploadTimeout(task, tasksCollection) {
  const { _id: taskId, updateTime } = task;
  const now = new Date();
  const updateTimeDate = new Date(updateTime);
  const timeDiff = now.getTime() - updateTimeDate.getTime();

  // 如果上传状态超过10分钟，标记为失败
  if (timeDiff > 10 * 60 * 1000) {
    console.log(`任务 ${taskId} 上传超时，标记为失败`);

    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '上传超时',
      updateTime: now
    });



    return { action: 'timeout_failed', previousStatus: 'uploading' };
  }

  return { action: 'waiting', status: 'uploading' };
}

/**
 * 检查MPS任务状态
 * 音频提取阶段由 MPS 回调机制处理，这里只检查超时情况
 */
async function checkMpsJobStatus(task, tasksCollection) {
  const { _id: taskId, mpsJobId, updateTime } = task;

  if (!mpsJobId) {
    console.log(`任务 ${taskId} 缺少mpsJobId，尝试重新提取音频`);
    return await startAudioExtraction(task, tasksCollection);
  }

  // 检查是否超时（音频提取阶段由 MPS 回调处理，不需要主动轮询）
  const now = new Date();
  const updateTimeDate = new Date(updateTime);
  const timeDiff = (now.getTime() - updateTimeDate.getTime()) / 1000 / 60; // 分钟

  // 如果音频提取超过30分钟，标记为失败
  if (timeDiff > 30) {
    console.log(`任务 ${taskId} 音频提取超时（${timeDiff.toFixed(1)}分钟），标记为失败`);

    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '音频提取超时，请重试',
      updateTime: now
    });

    return { action: 'timeout_failed', previousStatus: 'extracting_audio' };
  }

  // 音频提取正在进行中，等待 MPS 回调
  console.log(`任务 ${taskId} 音频提取进行中，已运行 ${timeDiff.toFixed(1)} 分钟，等待 MPS 回调`);
  return { action: 'waiting', status: 'extracting_audio' };
}

/**
 * 开始音频提取
 */
async function startAudioExtraction(task, tasksCollection) {
  const { _id: taskId, ossUrl } = task;

  try {
    console.log(`任务 ${taskId} 开始音频提取`);

    const result = await uniCloud.callFunction({
      name: 'process-video-task',
      data: {
        taskId: taskId,
        ossUrl: ossUrl,
        action: 'extract_audio'
      }
    });

    if (result.result.code === 200) {
      return { action: 'audio_extraction_started', result: result.result };
    } else {
      throw new Error(result.result.message || '音频提取启动失败');
    }
  } catch (error) {
    console.error(`任务 ${taskId} 音频提取启动失败：`, error);

    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '音频提取启动失败：' + error.message,
      updateTime: new Date()
    });



    throw error;
  }
}

/**
 * 检查语音识别状态
 */
async function checkRecognitionStatus(task, tasksCollection) {
  const { _id: taskId, audioOssUrl } = task;

  // 检查是否有音频文件地址，如果没有则重新开始语音识别
  if (!audioOssUrl) {
    console.log(`任务 ${taskId} 缺少audioOssUrl，尝试重新开始语音识别`);
    return await startSpeechRecognition(task, tasksCollection);
  }

  // 检查任务是否长时间停留在识别状态（超过10分钟）
  const now = new Date();
  const updateTime = new Date(task.updateTime);
  const timeDiff = (now - updateTime) / 1000 / 60; // 分钟

  if (timeDiff > 10) {
    console.log(`任务 ${taskId} 识别状态超时（${timeDiff.toFixed(1)}分钟），重新启动语音识别`);
    return await startSpeechRecognition(task, tasksCollection);
  }

  // 语音识别正在进行中，等待完成
  console.log(`任务 ${taskId} 语音识别进行中，已运行 ${timeDiff.toFixed(1)} 分钟`);
  return { action: 'waiting', status: 'recognizing' };
}

/**
 * 开始语音识别
 */
async function startSpeechRecognition(task, tasksCollection) {
  const { _id: taskId, audioOssUrl } = task;

  if (!audioOssUrl) {
    console.error(`任务 ${taskId} 缺少audioOssUrl，无法开始语音识别`);
    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '缺少音频文件地址',
      updateTime: new Date()
    });
    throw new Error('缺少音频文件地址');
  }

  try {
    console.log(`任务 ${taskId} 开始语音识别`);

    const result = await uniCloud.callFunction({
      name: 'process-video-task',
      data: {
        taskId: taskId,
        audioOssUrl: audioOssUrl,
        action: 'speech_recognition'
      }
    });

    if (result.result.code === 200) {
      return { action: 'speech_recognition_started', result: result.result };
    } else {
      throw new Error(result.result.message || '语音识别启动失败');
    }
  } catch (error) {
    console.error(`任务 ${taskId} 语音识别启动失败：`, error);
    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '语音识别启动失败：' + error.message,
      updateTime: new Date()
    });
    throw error;
  }
}

/**
 * 检查翻译状态
 */
async function checkTranslationStatus(task, tasksCollection) {
  const { _id: taskId, updateTime } = task;

  // 检查翻译是否超时（超过10分钟）
  const now = new Date();
  const updateTimeDate = new Date(updateTime);
  const timeDiff = (now.getTime() - updateTimeDate.getTime()) / 1000 / 60; // 分钟

  if (timeDiff > 10) {
    console.log(`任务 ${taskId} 翻译状态超时（${timeDiff.toFixed(1)}分钟），标记为失败`);

    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '翻译超时，请重试',
      updateTime: now
    });

    return { action: 'timeout_failed', previousStatus: 'translating' };
  }

  // 翻译正在进行中，等待完成
  console.log(`任务 ${taskId} 翻译进行中，已运行 ${timeDiff.toFixed(1)} 分钟`);
  return { action: 'waiting', status: 'translating' };
}

/**
 * 开始翻译
 */
async function startTranslation(task, tasksCollection) {
  const { _id: taskId } = task;

  try {
    console.log(`任务 ${taskId} 开始翻译`);

    const result = await uniCloud.callFunction({
      name: 'process-video-task',
      data: {
        taskId: taskId,
        action: 'translate'
      }
    });

    if (result.result.code === 200) {
      return { action: 'translation_started', result: result.result };
    } else {
      throw new Error(result.result.message || '翻译启动失败');
    }
  } catch (error) {
    console.error(`任务 ${taskId} 翻译启动失败：`, error);
    await tasksCollection.doc(taskId).update({
      status: 'failed',
      errorMessage: '翻译启动失败：' + error.message,
      updateTime: new Date()
    });
    throw error;
  }
}

/**
 * 检查视频合并状态
 */
async function checkMergingStatus(task, tasksCollection) {
  const { _id: taskId, mpsJobId } = task;

  console.log(`🎬 [调度器] 检查字幕烧录状态，TaskId: ${taskId}, mpsJobId: ${mpsJobId}`);

  if (!mpsJobId) {
    // 如果没有mpsJobId，说明还没开始合并，需要启动合并
    try {
      console.log(`🚀 [调度器] 任务 ${taskId} 开始启动字幕烧录`);

      const result = await uniCloud.callFunction({
        name: 'process-video-task',
        data: {
          taskId: taskId,
          action: 'merge_subtitle'
        }
      });

      if (result.result.code === 200) {
        console.log(`✅ [调度器] 任务 ${taskId} 字幕烧录启动成功`);
        return { action: 'merging_started', result: result.result };
      } else {
        console.error(`❌ [调度器] 任务 ${taskId} 字幕烧录启动失败:`, result.result.message);
        throw new Error(result.result.message || '视频合并启动失败');
      }
    } catch (error) {
      console.error(`任务 ${taskId} 视频合并启动失败：`, error);
      await tasksCollection.doc(taskId).update({
        status: 'failed',
        errorMessage: '视频合并启动失败：' + error.message,
        updateTime: new Date()
      });
      throw error;
    }
  } else {
    // 如果有mpsJobId，说明合并已经开始，检查状态
    console.log(`🔍 [调度器] 任务 ${taskId} 已有mpsJobId，检查字幕烧录状态`);
    try {
      const result = await uniCloud.callFunction({
        name: 'poll-mps-tasks',
        data: {
          taskId: taskId,
          mode: 'query'
        }
      });

      if (result.result.code === 200) {
        const { status } = result.result.data;
        if (status === 'success') {
          // 合并成功，任务应该已经自动标记为completed
          return { action: 'merging_completed', result: result.result };
        } else if (status === 'failed') {
          // 合并失败，任务应该已经标记为failed
          return { action: 'merging_failed', result: result.result };
        } else {
          // 仍在进行中
          return { action: 'waiting', status: 'merging' };
        }
      } else {
        throw new Error(result.result.message || '视频合并状态查询失败');
      }
    } catch (error) {
      console.error(`任务 ${taskId} 视频合并状态查询失败：`, error);
      return { action: 'error', error: error.message };
    }
  }
}

/**
 * 检查所有任务状态（用于调试和监控）
 */
async function checkAllTasks(tasksCollection) {
  console.log("检查所有任务状态");

  const tasksResult = await tasksCollection
    .orderBy('updateTime', 'desc')
    .limit(50) // 限制查询数量
    .get();

  const tasks = tasksResult.data || [];

  const summary = {
    total: tasks.length,
    byStatus: {},
    backgroundMode: 0
  };

  tasks.forEach(task => {
    // 统计各状态任务数量
    summary.byStatus[task.status] = (summary.byStatus[task.status] || 0) + 1;

    // 统计后台模式任务数量
    if (task.backgroundMode) {
      summary.backgroundMode++;
    }
  });

  return summary;
}


