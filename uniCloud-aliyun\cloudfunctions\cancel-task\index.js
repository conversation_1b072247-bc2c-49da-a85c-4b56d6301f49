// uniCloud云函数：取消视频处理任务
"use strict";

const createConfig = require("uni-config-center");
const Core = require("@alicloud/pop-core");

/**
 * 取消正在进行的视频处理任务
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（可选，用于权限验证）
 * @returns {Object} 取消结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("cancel-task 云函数被调用，参数：", { taskId, hasOpenid: !!openid });

    // ==================== 参数验证 ====================
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        code: 400,
        message: "缺少必要参数：taskId",
        data: null,
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.trim().length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        code: 400,
        message: "taskId格式不正确",
        data: null,
      };
    }

    // ==================== 数据库操作 ====================
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");
    const usersCollection = db.collection("users");

    let userId = null;

    // 如果提供了openid，进行用户验证
    if (openid) {
      const userResult = await usersCollection
        .where({ openid: openid })
        .field({ _id: true })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid);
        return {
          code: 404,
          message: "用户不存在",
          data: null,
        };
      }

      userId = userResult.data[0]._id;
      console.log("用户验证通过，userId：", userId);
    }

    // 查询任务信息
    let taskQuery = tasksCollection.where({ _id: taskId });

    // 如果有用户ID，添加权限过滤
    if (userId) {
      taskQuery = taskQuery.where({ userId: userId });
    }

    const taskResult = await taskQuery
      .field({
        _id: true,
        status: true,
        mpsJobId: true,
        userId: true,
        fileName: true,
        createTime: true,
        updateTime: true,
      })
      .limit(1)
      .get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务查询失败：任务不存在或无权限访问", taskId);
      return {
        code: 404,
        message: "任务不存在或无权限访问",
        data: null,
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务查询成功，当前状态：", taskData.status);

    // ==================== 业务逻辑验证 ====================

    // 检查任务状态，只允许取消进行中的任务
    const cancellableStatuses = [
      "uploading",
      "extracting_audio", 
      "recognizing",
      "translating",
      "merging"
    ];

    if (!cancellableStatuses.includes(taskData.status)) {
      let message = "任务无法取消";
      
      if (taskData.status === "completed") {
        message = "任务已完成，无法取消";
      } else if (taskData.status === "failed") {
        message = "任务已失败，无需取消";
      } else if (taskData.status === "cancelled") {
        message = "任务已被取消";
      } else {
        message = `任务状态为"${taskData.status}"，无法取消`;
      }

      console.warn("取消验证失败：任务状态不允许取消", taskId, taskData.status);
      return {
        code: 400,
        message: message,
        data: null,
      };
    }

    // ==================== 取消第三方服务 ====================
    const cancelResults = {
      mps: null,
      paraformer: null,
    };

    // 取消阿里云MPS任务
    if (taskData.mpsJobId) {
      try {
        console.log("尝试取消MPS任务，JobId：", taskData.mpsJobId);
        const mpsResult = await cancelMpsJob(taskData.mpsJobId);
        cancelResults.mps = mpsResult;
        console.log("MPS任务取消结果：", mpsResult);
      } catch (error) {
        console.error("取消MPS任务失败：", error.message);
        cancelResults.mps = { success: false, error: error.message };
        // 不抛出错误，继续执行后续逻辑
      }
    }

    // 语音识别现在使用 Whisper，无需特殊取消逻辑
    // Whisper 识别任务会在云函数执行完成后自动结束

    // ==================== 更新数据库状态 ====================
    const cancelledAt = new Date();
    
    const updateResult = await tasksCollection.doc(taskId).update({
      status: "cancelled",
      updateTime: cancelledAt,
      cancelledAt: cancelledAt,
      // 记录取消时的第三方服务状态
      cancelResults: cancelResults,
    });

    console.log("任务状态更新成功，更新记录数：", updateResult.updated);

    // ==================== 返回结果 ====================
    return {
      code: 200,
      message: "任务已成功取消",
      data: {
        taskId: taskId,
        status: "cancelled",
        cancelledAt: cancelledAt.toISOString(),
        previousStatus: taskData.status,
        thirdPartyResults: cancelResults,
      },
    };

  } catch (error) {
    console.error("cancel-task 云函数执行错误：", error);

    return {
      code: 500,
      message: "服务器内部错误：" + error.message,
      data: null,
    };
  }
};

/**
 * 取消阿里云MPS任务
 * @param {string} jobId - MPS任务ID
 * @returns {Promise<Object>} 取消结果
 */
async function cancelMpsJob(jobId) {
  try {
    // 获取阿里云MPS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mps",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");

    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云MPS配置错误，请配置访问密钥");
    }

    // 创建MPS客户端
    const mpsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mts.${regionId}.aliyuncs.com`,
      apiVersion: "2014-06-18",
    });

    // 调用取消任务API
    const cancelParams = {
      JobId: jobId,
    };

    console.log("调用MPS CancelJob API，参数：", cancelParams);

    const response = await mpsClient.request("CancelJob", cancelParams, {
      method: "POST",
    });

    console.log("MPS CancelJob API 响应：", response);

    return {
      success: true,
      jobId: jobId,
      response: response,
    };

  } catch (error) {
    console.error("取消MPS任务失败：", error);
    throw error;
  }
}


