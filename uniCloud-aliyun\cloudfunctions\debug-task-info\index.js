'use strict';

/**
 * 调试工具：查看任务详细信息
 */
exports.main = async (event, context) => {
  try {
    const { taskId } = event;
    
    if (!taskId) {
      return {
        code: 400,
        message: "缺少taskId参数"
      };
    }
    
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");
    
    // 获取任务详细信息
    const taskResult = await tasksCollection.doc(taskId).get();
    
    if (!taskResult.data || taskResult.data.length === 0) {
      return {
        code: 404,
        message: "任务不存在"
      };
    }
    
    const task = taskResult.data[0];
    
    console.log("=== 任务详细信息 ===");
    console.log("TaskId:", task._id);
    console.log("Status:", task.status);
    console.log("OssUrl:", task.ossUrl);
    console.log("AudioOssUrl:", task.audioOssUrl);
    console.log("SubtitleOssUrl:", task.subtitleOssUrl);
    console.log("FinalVideoUrl:", task.finalVideoUrl);
    console.log("AudioExtractionJobId:", task.audioExtractionJobId);
    console.log("SubtitleMergeJobId:", task.subtitleMergeJobId);
    console.log("ErrorMessage:", task.errorMessage);
    console.log("CreateTime:", task.createTime);
    console.log("UpdateTime:", task.updateTime);
    console.log("BackgroundMode:", task.backgroundMode);
    console.log("IsParsedVideo:", task.isParsedVideo);
    
    // 分析任务状态
    const analysis = {
      canStartMerging: false,
      missingFields: [],
      recommendations: []
    };
    
    if (task.status === 'merging') {
      if (!task.subtitleMergeJobId) {
        analysis.canStartMerging = true;
        analysis.recommendations.push("任务处于merging状态但没有subtitleMergeJobId，应该启动字幕烧录");
      } else {
        analysis.recommendations.push("任务已有subtitleMergeJobId，应该检查MPS任务状态");
      }
      
      if (!task.ossUrl) {
        analysis.missingFields.push("ossUrl");
      }
      if (!task.subtitleOssUrl) {
        analysis.missingFields.push("subtitleOssUrl");
      }
    }
    
    console.log("=== 分析结果 ===");
    console.log("Can Start Merging:", analysis.canStartMerging);
    console.log("Missing Fields:", analysis.missingFields);
    console.log("Recommendations:", analysis.recommendations);
    
    return {
      code: 200,
      message: "任务信息获取成功",
      data: {
        task: task,
        analysis: analysis
      }
    };
    
  } catch (error) {
    console.error("debug-task-info 执行错误：", error);
    return {
      code: 500,
      message: "获取任务信息失败: " + error.message
    };
  }
};
