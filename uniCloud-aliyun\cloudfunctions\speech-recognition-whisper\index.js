// uniCloud云函数：基于OpenAI Whisper的语音识别
// 参考 audio-subtitle-serverless.js 的最佳实践进行优化
"use strict";

const createConfig = require("uni-config-center");

// 常量配置
const CONFIG = {
  MAX_AUDIO_SIZE: 25 * 1024 * 1024, // 25MB音频文件大小限制
  DOWNLOAD_TIMEOUT: 30000, // 30秒下载超时
  API_TIMEOUT: 120000, // 2分钟API超时
  WHISPER_MODEL: "whisper-large-v3",
  RESPONSE_FORMAT: "verbose_json",
  TEMPERATURE: 0.0
};

/**
 * 使用OpenAI Whisper进行语音识别
 * 参考 audio-subtitle-serverless.js 的实现优化
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.audioOssUrl - 音频文件OSS地址
 * @param {string} event.sourceLanguage - 源语言代码，默认"auto"
 * @returns {Object} 识别结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();

  try {
    const { taskId, audioOssUrl, sourceLanguage = "auto" } = event;

    console.log("🎤 speech-recognition-whisper 云函数启动");
    console.log("📥 输入参数：", {
      taskId,
      audioOssUrl,
      sourceLanguage,
      timestamp: new Date().toISOString()
    });

    // 参数验证
    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    if (!audioOssUrl) {
      return createErrorResponse(400, "缺少必要参数：audioOssUrl");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证任务是否存在且状态正确
    const taskInfo = await validateTask(tasksCollection, taskId);

    // 获取和验证API配置
    const { apiKey, baseUrl, model } = await getAndValidateConfig();

    console.log("✅ 配置验证通过", {
      baseUrl,
      model,
      hasApiKey: !!apiKey,
      taskValidated: !!taskInfo
    });

    try {
      // 执行语音识别流程
      console.log("🎤 开始语音识别流程...");

      const recognitionResult = await performWhisperRecognition(
        apiKey,
        baseUrl,
        model,
        audioOssUrl,
        sourceLanguage
      );

      // 验证识别结果
      if (!recognitionResult?.srtContent?.trim()) {
        console.error("❌ Whisper识别结果为空：", recognitionResult);
        throw new Error("Whisper语音识别返回空结果，请检查音频文件和API配置");
      }

      console.log("✅ 语音识别完成", {
        srtLength: recognitionResult.srtContent.length,
        detectedLanguage: recognitionResult.detectedLanguage,
        segmentCount: recognitionResult.segmentCount || 0
      });

      // 异步调用 process-video-task 上传SRT文件并处理后续流程
      console.log("📤 异步调用 process-video-task 上传字幕文件...");
      uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          action: "upload_srt",
          srtContent: recognitionResult.srtContent,
        },
      }).then(uploadResult => {
        console.log("SRT上传和处理完成：", uploadResult);
      }).catch(error => {
        console.error("SRT上传和处理失败：", error);
      });

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`🎉 语音识别任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("语音识别成功", {
        taskId,
        status: "completed",
        srtLength: recognitionResult.srtContent.length,
        detectedLanguage: recognitionResult.detectedLanguage,
        processingTime,
        segmentCount: recognitionResult.segmentCount || 0,
        message: "语音识别完成，SRT文件正在异步上传和处理中"
      });

    } catch (error) {
      console.error("❌ 语音识别失败：", error);

      // 更新任务状态为失败
      try {
        await tasksCollection.doc(taskId).update({
          status: "failed",
          errorMessage: "语音识别失败：" + error.message,
          updateTime: new Date(),
        });
      } catch (updateError) {
        console.error("⚠️ 更新任务状态失败：", updateError);
      }

      throw error;
    }

  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("❌ speech-recognition-whisper 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
      timestamp: new Date().toISOString()
    });

    return createErrorResponse(500, "语音识别失败: " + error.message, {
      processingTime,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {Object} data - 响应数据
 * @returns {Object} 标准化成功响应
 */
function createSuccessResponse(message, data) {
  return {
    code: 200,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建错误响应
 * @param {number} code - 错误代码
 * @param {string} message - 错误消息
 * @param {Object} extra - 额外信息
 * @returns {Object} 标准化错误响应
 */
function createErrorResponse(code, message, extra = {}) {
  return {
    code,
    message,
    timestamp: new Date().toISOString(),
    ...extra
  };
}

/**
 * 验证任务状态
 * @param {Object} tasksCollection - 任务集合
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务信息
 */
async function validateTask(tasksCollection, taskId) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (task.status !== "recognizing") {
    console.warn("⚠️ 任务状态不正确", {
      expected: "recognizing",
      actual: task.status,
      taskId
    });
  }

  return task;
}

/**
 * 获取和验证API配置
 * @returns {Promise<Object>} API配置
 */
async function getAndValidateConfig() {
  const openaiConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.WHISPER_MODEL,
    },
  });

  const apiKey = openaiConfig.config("apiKey");
  const baseUrl = openaiConfig.config("baseUrl");
  const model = openaiConfig.config("model");

  if (!apiKey) {
    throw new Error("OpenAI API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl, model };
}

/**
 * 解析API错误响应
 * @param {Object} response - API响应
 * @returns {Object} 错误信息
 */
function parseApiError(response) {
  let message = "未知错误";
  try {
    const errorData = typeof response.data === 'string' ?
      JSON.parse(response.data) : response.data;
    if (errorData?.error) {
      message = errorData.error.message || errorData.error;
    }
    console.error("🔍 API错误详情：", errorData);
  } catch (e) {
    console.error("🔍 原始响应内容：", response.data);
  }
  return { message, status: response.status };
}

/**
 * 解析API响应数据
 * @param {Object} response - API响应
 * @returns {Object} 解析后的数据
 */
function parseApiResponse(response) {
  try {
    if (typeof response.data === 'string') {
      return JSON.parse(response.data);
    }
    return response.data;
  } catch (parseError) {
    console.error("❌ 解析Whisper响应失败：", {
      error: parseError.message,
      dataType: typeof response.data,
      dataPreview: String(response.data).substring(0, 200)
    });
    throw new Error("解析Whisper响应失败: " + parseError.message);
  }
}

/**
 * 处理Whisper segments数据
 * @param {Object} result - Whisper API结果
 * @returns {Object} 处理后的SRT数据
 */
function processWhisperSegments(result) {
  // 检查是否有segments数据
  if (!result.segments || !Array.isArray(result.segments) || result.segments.length === 0) {
    console.warn("⚠️ Whisper响应中没有segments数据，使用整体文本");

    // 检查是否有文本内容
    if (!result.text?.trim()) {
      console.error("❌ Whisper响应中既没有segments也没有text内容");
      throw new Error("Whisper识别结果为空，请检查API配置和音频文件");
    }

    // 创建单个segment包含整个文本
    const duration = result.duration || 60;
    const segments = [{
      start: 0,
      end: duration,
      text: result.text.trim()
    }];

    const srtContent = convertSegmentsToSRT(segments);
    console.log("📝 使用整体文本创建SRT");

    return {
      srtContent,
      segmentCount: 1
    };
  }

  // 转换segments为SRT格式
  console.log(`🔄 转换 ${result.segments.length} 个segments为SRT格式`);
  const srtContent = convertSegmentsToSRT(result.segments);

  return {
    srtContent,
    segmentCount: result.segments.length
  };
}

/**
 * 执行Whisper语音识别
 * 参考 audio-subtitle-serverless.js 的优化实现
 * @param {string} apiKey - API密钥
 * @param {string} baseUrl - API基础URL
 * @param {string} model - 模型名称
 * @param {string} audioOssUrl - 音频文件URL
 * @param {string} sourceLanguage - 源语言代码
 * @returns {Promise<Object>} 识别结果
 */
async function performWhisperRecognition(apiKey, baseUrl, model, audioOssUrl, sourceLanguage) {
  const recognitionStartTime = Date.now();
  console.log("🎤 开始Whisper语音识别", {
    audioUrl: audioOssUrl,
    sourceLanguage,
    model
  });

  // 下载音频文件到内存
  const audioBuffer = await downloadAudioToMemory(audioOssUrl);
  const downloadTime = (Date.now() - recognitionStartTime) / 1000;

  console.log("✅ 音频下载完成", {
    size: `${(audioBuffer.length / 1024 / 1024).toFixed(1)}MB`,
    downloadTime: `${downloadTime.toFixed(2)}秒`
  });

  const url = `${baseUrl}/v1/audio/transcriptions`;

  try {
    // 发送Whisper API请求
    const apiStartTime = Date.now();
    const response = await sendWhisperRequest(url, audioBuffer, apiKey, model, sourceLanguage);
    const apiTime = (Date.now() - apiStartTime) / 1000;

    console.log("📡 Whisper API响应", {
      status: response.status,
      apiTime: `${apiTime.toFixed(2)}秒`
    });

    if (response.status !== 200) {
      const errorInfo = parseApiError(response);
      console.error("❌ Whisper API请求失败", errorInfo);
      throw new Error(`Whisper API请求失败，状态码: ${response.status}, 错误: ${errorInfo.message}`);
    }

    // 解析API响应
    const result = parseApiResponse(response);
    const totalTime = (Date.now() - recognitionStartTime) / 1000;

    console.log("✅ Whisper API响应解析完成", {
      hasLanguage: !!result.language,
      hasText: !!result.text,
      textLength: result.text?.length || 0,
      hasSegments: !!result.segments,
      segmentsLength: result.segments?.length || 0,
      duration: result.duration,
      totalTime: `${totalTime.toFixed(2)}秒`
    });

    if (result.language) {
      console.log("🌐 检测语言：", result.language);
    }

    if (result.text) {
      const preview = result.text.length > 200 ?
        result.text.substring(0, 200) + "..." :
        result.text;
      console.log("📝 识别文本预览：", preview);
    }

    // 处理segments数据
    const { srtContent, segmentCount } = processWhisperSegments(result);

    console.log(`🎯 SRT生成完成`, {
      srtLength: srtContent.length,
      segmentCount,
      detectedLanguage: result.language
    });

    return {
      srtContent,
      detectedLanguage: result.language,
      segmentCount,
      processingTime: totalTime
    };
    
  } catch (error) {
    console.error("Whisper API调用失败：", error);
    throw error;
  }
}

/**
 * 发送Whisper API请求
 * 使用 form-data 库构建标准的 multipart/form-data 请求
 * @param {string} url - API URL
 * @param {Buffer} audioBuffer - 音频数据
 * @param {string} apiKey - API密钥
 * @param {string} model - 模型名称
 * @param {string} sourceLanguage - 源语言
 * @returns {Promise<Object>} API响应
 */
async function sendWhisperRequest(url, audioBuffer, apiKey, model, sourceLanguage) {
  const FormData = require('form-data');

  console.log('🎤 正在转录音频...');

  const form = new FormData();
  form.append('file', audioBuffer, {
    filename: 'audio.mp3',
    contentType: 'audio/mpeg'
  });
  form.append('model', model);
  form.append('response_format', CONFIG.RESPONSE_FORMAT);
  form.append('temperature', CONFIG.TEMPERATURE.toString());

  // 只有在指定语言时才添加language字段
  if (sourceLanguage && sourceLanguage !== "auto") {
    form.append('language', sourceLanguage);
  }

  const headers = {
    'Authorization': `Bearer ${apiKey}`,
    'Accept': 'application/json',
    'User-Agent': 'uniCloud-whisper-client/1.0',
    ...form.getHeaders()
  };

  console.log("📡 发送Whisper识别请求", {
    url,
    model,
    response_format: CONFIG.RESPONSE_FORMAT,
    temperature: CONFIG.TEMPERATURE,
    language: sourceLanguage !== "auto" ? sourceLanguage : "auto",
    fileSize: `${(audioBuffer.length / 1024 / 1024).toFixed(1)}MB`
  });

  // 使用 form-data 发送请求
  return new Promise((resolve, reject) => {
    try {
      const urlObj = new URL(url);
      const options = {
        protocol: urlObj.protocol,
        host: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname,
        method: 'POST',
        headers: headers,
        timeout: CONFIG.API_TIMEOUT
      };

      console.log("🔗 连接信息", {
        protocol: options.protocol,
        host: options.host,
        port: options.port,
        path: options.path
      });

      const request = form.submit(options, (error, response) => {
        if (error) {
          console.error("❌ form-data 提交错误:", error);
          reject(error);
          return;
        }

        let data = '';
        response.on('data', (chunk) => {
          data += chunk;
        });

        response.on('end', () => {
          console.log("✅ 请求完成", {
            statusCode: response.statusCode,
            dataLength: data.length
          });

          resolve({
            status: response.statusCode,
            data: data
          });
        });

        response.on('error', (err) => {
          console.error("❌ 响应流错误:", err);
          reject(err);
        });
      });

      request.on('error', (err) => {
        console.error("❌ 请求错误:", err);
        reject(err);
      });

      request.setTimeout(CONFIG.API_TIMEOUT, () => {
        console.error("❌ 请求超时");
        request.destroy();
        reject(new Error('Whisper API请求超时'));
      });

    } catch (urlError) {
      console.error("❌ URL解析错误:", urlError);
      reject(new Error(`URL解析失败: ${urlError.message}`));
    }
  });
}

/**
 * 下载音频文件到内存
 * 参考 audio-subtitle-serverless.js 的优化实现
 * @param {string} audioUrl - 音频文件地址
 * @returns {Promise<Buffer>} 音频文件Buffer
 */
async function downloadAudioToMemory(audioUrl) {
  try {
    console.log("📥 开始下载音频文件：", audioUrl);

    const response = await uniCloud.httpclient.request(audioUrl, {
      method: 'GET',
      timeout: CONFIG.DOWNLOAD_TIMEOUT,
      dataType: 'stream'
    });

    if (response.status !== 200) {
      throw new Error(`下载失败: HTTP ${response.status}`);
    }

    const audioBuffer = response.data;
    const fileSize = audioBuffer.length;

    // 检查文件大小限制
    if (fileSize > CONFIG.MAX_AUDIO_SIZE) {
      throw new Error(`音频文件超过${CONFIG.MAX_AUDIO_SIZE / 1024 / 1024}MB限制`);
    }

    console.log("✅ 音频下载完成", {
      size: `${(fileSize / 1024 / 1024).toFixed(1)}MB`,
      url: audioUrl
    });

    return audioBuffer;
  } catch (error) {
    console.error("❌ 下载音频文件失败：", error);
    throw new Error(`下载音频文件失败: ${error.message}`);
  }
}

/**
 * 将segments转换为SRT格式
 * 参考simple_subtitle_demo.py的实现
 * @param {Array} segments - Whisper返回的segments数组
 * @returns {string} SRT格式字符串
 */
function convertSegmentsToSRT(segments) {
  let srtContent = "";
  
  segments.forEach((segment, index) => {
    const startTime = formatSRTTime(segment.start);
    const endTime = formatSRTTime(segment.end);
    const text = segment.text.trim();

    if (text) {
      srtContent += `${index + 1}\n`;
      srtContent += `${startTime} --> ${endTime}\n`;
      srtContent += `${text}\n\n`;
    }
  });

  return srtContent.trim();
}

/**
 * 格式化SRT时间格式
 * 参考simple_subtitle_demo.py的实现
 * @param {number} seconds - 秒数
 * @returns {string} SRT时间格式 (HH:MM:SS,mmm)
 */
function formatSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

