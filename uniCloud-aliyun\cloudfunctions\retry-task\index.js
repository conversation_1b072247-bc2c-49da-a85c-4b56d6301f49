"use strict";

/**
 * 重新处理任务云函数
 *
 * 功能说明：
 * 1. 验证用户权限，确保只能重试自己的任务
 * 2. 验证 ossUrl 字段有值，确保有原始视频文件可以重新处理
 * 3. 检查任务状态，只允许重试失败或已取消的任务
 * 4. 重置任务状态和相关字段，准备重新处理
 * 5. 触发重新处理流程
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（可选，用于权限验证）
 * @returns {Object} 重试结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("retry-task 云函数被调用，参数：", {
      taskId,
      hasOpenid: !!openid,
      clientIP: CLIENTIP,
    });

    // ==================== 参数验证 ====================
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        code: 400,
        message: "taskId格式不正确",
      };
    }

    // ==================== 数据库连接 ====================
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");
    const usersCollection = db.collection("users");

    // ==================== 用户权限验证（如果提供了openid） ====================
    let userId = null;

    if (openid) {
      console.log("开始验证用户权限...");

      // 验证用户是否存在
      const userResult = await usersCollection
        .where({
          openid: openid,
        })
        .field({
          _id: true,
          status: true,
        })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid.substring(0, 8) + "***");
        return {
          code: 401,
          message: "用户不存在，请先登录",
        };
      }

      const userData = userResult.data[0];
      userId = userData._id;

      // 检查用户状态
      if (userData.status === "banned") {
        console.error("用户验证失败：用户已被封禁", userId);
        return {
          code: 403,
          message: "用户已被封禁，无法执行此操作",
        };
      }

      console.log("用户验证通过，userId：", userId);
    }

    // ==================== 任务验证 ====================
    console.log("开始验证任务信息...");

    // 查询任务信息
    const taskResult = await tasksCollection
      .where({
        _id: taskId,
        userId: userId,
      })
      .field({
        _id: true,
        status: true,
        fileName: true,
        ossUrl: true,
        audioOssUrl: true,
        subtitleOssUrl: true,
        finalVideoUrl: true,
        errorMessage: true,
        mpsJobId: true,
        createTime: true,
        userId: true,
      })
      .limit(1)
      .get();
    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务验证失败：任务不存在或无权限访问", taskId);
      return {
        code: 404,
        message: "任务不存在或无权限访问",
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务验证通过，任务状态：", taskData, taskData.status);

    // ==================== 业务逻辑验证 ====================

    // 1. 验证 ossUrl 字段必须有值
    if (!taskData.ossUrl || taskData.ossUrl.trim() === "") {
      console.error("重试验证失败：ossUrl字段为空，无法重新处理", taskId);
      return {
        code: 422,
        message: "原始视频文件不存在，无法重新处理",
      };
    }

    // 2. 检查任务状态，只允许重试失败、已取消或已删除的任务
    const retryableStatuses = ["failed", "cancelled", "deleted"];

    if (!retryableStatuses.includes(taskData.status)) {
      console.warn("重试验证失败：任务状态不允许重试", taskId, taskData.status);
      return {
        code: 409,
        message: `任务状态为"${taskData.status}"，无法重新处理。只有失败或已取消的任务才能重新处理`,
      };
    }

    // 3. 检查是否正在处理中（防止重复提交）
    const processingStatuses = [
      "uploading",
      "extracting_audio",
      "recognizing",
      "translating",
      "merging",
    ];

    if (processingStatuses.includes(taskData.status)) {
      console.warn("重试验证失败：任务正在处理中", taskId, taskData.status);
      return {
        code: 409,
        message: "任务正在处理中，请勿重复提交",
      };
    }

    // ==================== 执行重试操作 ====================
    console.log("开始执行重试操作...");

    try {
      // 重置任务状态和相关字段
      const resetData = {
        status: "extracting_audio", // 重置为初始状态
        errorMessage: "", // 清空错误信息
        audioOssUrl: "", // 清空中间产物
        subtitleOssUrl: "",
        finalVideoUrl: "",
        mpsJobId: "", // 清空外部任务ID
        updateTime: new Date(),
        retryAt: new Date(), // 记录重试时间
        retryCount: (taskData.retryCount || 0) + 1, // 增加重试次数
      };

      const updateResult = await tasksCollection.doc(taskId).update(resetData);

      console.log("任务状态重置成功，更新记录数：", updateResult.updated);

      // ==================== 触发重新处理流程 ====================

      // 调用处理视频任务的云函数，开始重新处理
      try {
        const processResult = await uniCloud.callFunction({
          name: "process-video-task",
          data: {
            action: "extract_audio",
            taskId: taskId,
            ossUrl: taskData.ossUrl,
          },
        });

        console.log("重新处理流程启动结果：", processResult);

        if (processResult.result && processResult.result.code !== 200) {
          console.warn("重新处理流程启动失败：", processResult.result.message);
          // 如果启动失败，将状态改回失败
          await tasksCollection.doc(taskId).update({
            status: "failed",
            errorMessage: "重新处理启动失败：" + processResult.result.message,
            updateTime: new Date(),
          });

          return {
            code: 500,
            message: "重新处理启动失败：" + processResult.result.message,
          };
        }
      } catch (processError) {
        console.error("调用处理流程失败：", processError);

        // 如果调用失败，将状态改回失败
        await tasksCollection.doc(taskId).update({
          status: "failed",
          errorMessage: "重新处理启动失败：" + processError.message,
          updateTime: new Date(),
        });

        return {
          code: 500,
          message: "重新处理启动失败，请稍后重试",
        };
      }

      // ==================== 返回成功结果 ====================
      return {
        code: 200,
        message: "重新处理已启动",
        data: {
          taskId: taskId,
          fileName: taskData.videoInfo?.fileName,
          oldStatus: taskData.status,
          newStatus: "uploading",
          retryCount: resetData.retryCount,
          retryAt: resetData.retryAt,
        },
      };
    } catch (updateError) {
      console.error("执行重试操作失败：", updateError);
      throw updateError;
    }
  } catch (error) {
    console.error("retry-task 云函数执行错误：", error);

    return {
      code: 500,
      message: "服务器内部错误",
      error: error.message,
    };
  }
};
