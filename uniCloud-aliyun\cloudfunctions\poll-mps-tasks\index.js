// uniCloud云函数：定时轮询阿里云MPS任务状态
"use strict";

const Core = require("@alicloud/pop-core");
const createConfig = require("uni-config-center");

/**
 * 定时轮询阿里云MPS任务状态
 * 支持定时批量处理和按需查询两种模式
 *
 * @param {Object} event
 * @param {string} event.taskId - 可选，指定要查询的任务ID（用于前端轮询）
 * @param {string} event.mode - 查询模式：'batch'(批量处理，定时器调用) 或 'query'(查询指定任务，前端调用)
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log("poll-mps-tasks 云函数被调用，参数：", event);

    const { taskId, mode = 'batch' } = event;
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 判断调用模式
    if (mode === 'query' && taskId) {
      // 前端查询指定任务模式
      console.log(`查询指定任务 ${taskId} 的MPS状态`);

      const taskInfo = await tasksCollection.doc(taskId).get();
      if (!taskInfo.data || taskInfo.data.length === 0) {
        return {
          code: 404,
          message: "任务不存在",
          data: { taskId: taskId }
        };
      }

      const task = taskInfo.data[0];

      // 如果任务没有任何MPS JobId，说明还未到MPS处理阶段
      if (!task.audioExtractionJobId && !task.subtitleMergeJobId) {
        return {
          code: 200,
          message: "任务尚未开始MPS处理",
          data: {
            taskId: taskId,
            status: task.status,
            message: "任务尚未开始MPS处理阶段"
          }
        };
      }

      // 执行单次检查
      const checkResult = await checkSingleTask(task);

      return {
        code: 200,
        message: "查询完成",
        data: checkResult
      };

    } else {
      // 定时批量处理模式
      console.log("开始批量处理所有待处理的MPS任务");

      // 查询所有状态为 "extracting_audio" 或 "merging" 且有相应JobId的任务
      const pendingTasksResult = await tasksCollection
        .where({
          $or: [
            {
              status: "extracting_audio",
              audioExtractionJobId: db.command.exists(true)
            },
            {
              status: "merging",
              subtitleMergeJobId: db.command.exists(true)
            }
          ]
        })
        .get();

      const pendingTasks = pendingTasksResult.data || [];
      console.log(`找到 ${pendingTasks.length} 个待处理的MPS任务`);

      if (pendingTasks.length === 0) {
        return {
          code: 200,
          message: "没有待处理的MPS任务",
          data: {
            processedCount: 0,
            results: []
          }
        };
      }

      // 批量处理任务
      const results = await batchProcessTasks(pendingTasks);

      return {
        code: 200,
        message: `批量处理完成，共处理 ${pendingTasks.length} 个任务`,
        data: {
          processedCount: pendingTasks.length,
          results: results
        }
      };
    }

  } catch (error) {
    console.error("poll-mps-tasks 云函数执行错误：", error);

    return {
      code: 500,
      message: "MPS任务处理失败: " + error.message,
    };
  }
};

/**
 * 批量处理待处理的MPS任务
 * @param {Array} tasks - 待处理的任务列表
 * @returns {Promise<Array>} 处理结果列表
 */
async function batchProcessTasks(tasks) {
  const results = [];

  console.log(`开始批量处理 ${tasks.length} 个MPS任务`);

  // 并发处理任务，但限制并发数量避免过载
  const concurrencyLimit = 3; // MPS API调用限制更严格，最多同时处理3个任务
  const chunks = [];

  for (let i = 0; i < tasks.length; i += concurrencyLimit) {
    chunks.push(tasks.slice(i, i + concurrencyLimit));
  }

  for (const chunk of chunks) {
    const chunkPromises = chunk.map(async (task) => {
      try {
        console.log(`处理MPS任务 ${task._id}`);
        const result = await checkSingleTask(task);
        return {
          taskId: task._id,
          success: true,
          result: result
        };
      } catch (error) {
        console.error(`处理MPS任务 ${task._id} 失败：`, error);
        return {
          taskId: task._id,
          success: false,
          error: error.message
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);

    // 在处理下一批之前稍作延迟，避免API调用过于频繁
    if (chunks.indexOf(chunk) < chunks.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000)); // MPS需要更长延迟
    }
  }

  console.log(`MPS批量处理完成，成功: ${results.filter(r => r.success).length}，失败: ${results.filter(r => !r.success).length}`);

  return results;
}

/**
 * 检查单个MPS任务的状态
 * @param {Object} task - 任务对象
 * @returns {Promise<Object>} 检查结果
 */
async function checkSingleTask(task) {
  try {
    // 根据任务状态确定要查询的JobId
    const jobId = task.status === "extracting_audio"
      ? task.audioExtractionJobId
      : task.subtitleMergeJobId;

    console.log(`检查MPS任务 ${task._id}，JobId: ${jobId}`);

    // 获取阿里云MPS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mps",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");
    const outputBucket = aliyunConfig.config("outputBucket");

    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云MPS配置错误，请配置访问密钥");
    }

    // 创建MPS客户端
    const mpsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mts.${regionId}.aliyuncs.com`,
      apiVersion: "2014-06-18",
    });

    if (!jobId) {
      console.log(`任务 ${task._id} 缺少对应的JobId，跳过查询`);
      return {
        taskId: task._id,
        status: "skipped",
        message: "缺少对应的JobId"
      };
    }

    // 查询任务状态
    const response = await queryMpsJobStatus(mpsClient, jobId);
    console.log(`MPS任务 ${task._id} (JobId: ${jobId}) 状态：`, response.State);

    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    if (response.State === "TranscodeSuccess") {
      // 转码成功，根据当前任务状态进行不同处理
      console.log(`MPS任务 ${task._id} 转码成功，当前状态：${task.status}`);

      // 添加额外的验证：根据输出文件类型确定任务类型
      let actualTaskType = null;
      if (response.Output && response.Output.OutputFile) {
        const outputPath = response.Output.OutputFile.Object;
        if (outputPath.includes('/audio/') || outputPath.endsWith('.mp3')) {
          actualTaskType = 'audio_extraction';
        } else if (outputPath.includes('/final/') || outputPath.endsWith('.mp4')) {
          actualTaskType = 'subtitle_merging';
        }
        console.log(`根据输出文件路径判断任务类型：${actualTaskType}，输出路径：${outputPath}`);
      }

      if (task.status === "extracting_audio") {
        // 音频提取完成
        if (actualTaskType === 'subtitle_merging') {
          console.warn("任务状态为音频提取，但输出文件为视频，可能存在状态同步问题");
        }
        return await handleAudioExtractionSuccess(task, response, tasksCollection, outputBucket, regionId);
      } else if (task.status === "merging") {
        // 字幕烧录完成
        console.log(`字幕烧录任务完成，输出文件类型：${actualTaskType}`);

        // 检查输出文件类型是否匹配
        if (actualTaskType === 'audio_extraction') {
          console.error(`❌ 任务状态不匹配：任务状态为 merging（字幕烧录），但MPS输出的是音频文件`);
          console.error(`❌ 这可能表示：1) JobId错误 2) 任务状态不同步 3) MPS配置错误`);
          console.error(`❌ 输出路径：${response.Output.OutputFile.Object}`);
          console.error(`❌ 预期输出：final/{taskId}.mp4`);

          // 标记任务为失败
          await tasksCollection.doc(task._id).update({
            status: "failed",
            errorMessage: "字幕烧录任务配置错误：MPS返回音频文件而非视频文件",
            updateTime: new Date(),
          });

          return {
            taskId: task._id,
            status: "failed",
            message: "字幕烧录任务配置错误：MPS返回音频文件而非视频文件"
          };
        }

        return await handleSubtitleMergingSuccess(task, response, tasksCollection, outputBucket, regionId);
      }

    } else if (response.State === "TranscodeFail") {
      // 转码失败
      console.error(`MPS任务 ${task._id} 转码失败`);

      const errorMessage = task.status === "extracting_audio"
        ? `音频提取失败：${response.State}`
        : `字幕烧录失败：${response.State}`;

      await tasksCollection.doc(task._id).update({
        status: "failed",
        errorMessage: errorMessage,
        updateTime: new Date(),
      });

      return {
        taskId: task._id,
        status: "failed",
        message: errorMessage
      };

    } else {
      // 仍在进行中
      return {
        taskId: task._id,
        status: "running",
        message: `MPS状态：${response.State}`
      };
    }

  } catch (error) {
    console.error(`检查MPS任务 ${task._id} 失败：`, error);
    return {
      taskId: task._id,
      status: "error",
      message: "检查失败：" + error.message
    };
  }
}

/**
 * 查询MPS任务状态
 * @param {Object} mpsClient - MPS客户端
 * @param {string} jobId - 任务ID
 * @returns {Promise<Object>} 任务状态信息
 */
async function queryMpsJobStatus(mpsClient, jobId) {
  const queryParams = {
    JobIds: jobId,
  };

  console.log("调用MPS QueryJobList API，参数：", queryParams);

  const response = await mpsClient.request("QueryJobList", queryParams, {
    method: "POST",
  });

  console.log("MPS QueryJobList API 响应：", response);

  if (response.JobList && response.JobList.Job && response.JobList.Job.length > 0) {
    return response.JobList.Job[0];
  } else {
    throw new Error("MPS任务查询结果为空");
  }
}

/**
 * 处理音频提取成功
 * @param {Object} task - 任务对象
 * @param {Object} mpsResponse - MPS响应
 * @param {Object} tasksCollection - 任务集合
 * @param {string} outputBucket - 输出bucket
 * @param {string} regionId - 区域ID
 * @returns {Promise<Object>} 处理结果
 */
async function handleAudioExtractionSuccess(task, mpsResponse, tasksCollection, outputBucket, regionId) {
  console.log("音频提取成功，开始查询音频文件地址并启动语音识别");

  if (mpsResponse.Output && mpsResponse.Output.OutputFile) {
    const outputFile = mpsResponse.Output.OutputFile;
    const audioOssUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${outputFile.Object}`;

    console.log("获取到音频文件地址：", audioOssUrl);

    // 尝试从MPS响应中获取视频时长信息
    let videoDuration = task.duration || 0;
    
    if (mpsResponse.Input && mpsResponse.Input.InputFile && mpsResponse.Input.InputFile.Properties) {
      const properties = mpsResponse.Input.InputFile.Properties;
      if (properties.Duration) {
        // MPS返回的时长通常是字符串格式的秒数
        videoDuration = parseFloat(properties.Duration);
        console.log("从MPS响应中获取到视频时长：", videoDuration, "秒");
      }
    }

    // 构建更新数据
    const updateData = {
      status: "recognizing",
      audioOssUrl: audioOssUrl,
      updateTime: new Date(),
    };

    // 如果获取到了有效的视频时长且当前时长无效，则更新时长
    if (videoDuration > 0 && (!task.duration || task.duration <= 0)) {
      updateData.duration = videoDuration;
      console.log("更新任务时长为：", videoDuration, "秒");
    }

    // 更新任务状态
    await tasksCollection.doc(task._id).update(updateData);

    // 调用语音识别功能
    const processResult = await uniCloud.callFunction({
      name: "process-video-task",
      data: {
        taskId: task._id,
        audioOssUrl: audioOssUrl,
        action: "speech_recognition",
      },
    });

    console.log("调用语音识别结果：", processResult);

    return {
      taskId: task._id,
      status: "success",
      message: "音频提取成功，已启动语音识别",
      audioOssUrl: audioOssUrl
    };
  } else {
    console.error("无法获取音频文件地址");
    await tasksCollection.doc(task._id).update({
      status: "failed",
      errorMessage: "无法获取音频文件地址",
      updateTime: new Date(),
    });

    return {
      taskId: task._id,
      status: "failed",
      message: "无法获取音频文件地址"
    };
  }
}

/**
 * 处理字幕烧录成功
 * @param {Object} task - 任务对象
 * @param {Object} mpsResponse - MPS响应
 * @param {Object} tasksCollection - 任务集合
 * @param {string} outputBucket - 输出bucket
 * @param {string} regionId - 区域ID
 * @returns {Promise<Object>} 处理结果
 */
async function handleSubtitleMergingSuccess(task, mpsResponse, tasksCollection, outputBucket, regionId) {
  console.log("🎬 [字幕烧录检查] 步骤1: 开始处理字幕烧录成功结果");
  console.log("🆔 [字幕烧录检查] TaskId：", task._id);

  console.log("🎬 [字幕烧录检查] 步骤2: 检查MPS响应结构");
  if (mpsResponse.Output && mpsResponse.Output.OutputFile) {
    const outputFile = mpsResponse.Output.OutputFile;
    console.log("📋 [字幕烧录检查] MPS返回的输出文件信息：", JSON.stringify(outputFile, null, 2));
    
    console.log("🎬 [字幕烧录检查] 步骤3: 解析输出文件路径");
    // 字幕烧录任务应该输出视频文件到 final/ 目录
    let finalVideoUrl;

    // 检查 MPS 返回的输出文件路径
    const outputPath = outputFile.Object;
    console.log("📁 [字幕烧录检查] MPS返回的输出文件路径：", outputPath);
    console.log("🔍 [字幕烧录检查] 预期路径格式：final/{taskId}.mp4");

    if (outputPath.includes('/final/') && outputPath.endsWith('.mp4')) {
      // 正确的视频文件路径
      finalVideoUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/${outputPath}`;
      console.log("✅ [字幕烧录检查] 步骤4: MPS返回了正确的视频文件路径");
      console.log("🎯 [字幕烧录检查] 最终视频URL：", finalVideoUrl);
    } else {
      // MPS 返回了错误的路径，使用预期的视频文件路径
      console.warn("⚠️ [字幕烧录检查] 步骤4: MPS返回了错误的文件路径");
      console.warn("❌ [字幕烧录检查] 实际返回：", outputPath);
      console.warn("✅ [字幕烧录检查] 应该返回：final/{taskId}.mp4");

      finalVideoUrl = `https://${outputBucket}.oss-${regionId}.aliyuncs.com/final/${task._id}.mp4`;
      console.log("🔧 [字幕烧录检查] 使用预期的视频文件路径：", finalVideoUrl);

      console.log("🎬 [字幕烧录检查] 步骤5: 验证视频文件是否真的存在");
      console.log("🔍 [字幕烧录检查] 检查URL：", finalVideoUrl);

      // 验证文件是否真的存在
      try {
        const response = await uniCloud.httpclient.request(finalVideoUrl, {
          method: 'HEAD',
          timeout: 10000
        });

        console.log("📡 [字幕烧录检查] 文件验证响应状态：", response.status);
        console.log("📋 [字幕烧录检查] 响应头：", JSON.stringify(response.headers, null, 2));

        if (response.status === 200) {
          const fileSize = response.headers['content-length'];
          console.log("✅ [字幕烧录检查] 步骤6: 视频文件确实存在");
          console.log("📏 [字幕烧录检查] 文件大小：", fileSize, "字节");

          if (fileSize && parseInt(fileSize) > 0) {
            console.log("✅ [字幕烧录检查] 文件大小正常，继续处理");
          } else {
            console.warn("⚠️ [字幕烧录检查] 文件大小为0或未知");
          }
        } else {
          console.warn("❌ [字幕烧录检查] 步骤6: 视频文件不存在");
          console.warn("📊 [字幕烧录检查] HTTP状态码：", response.status);

          // 如果文件不存在，不要更新为完成状态
          await tasksCollection.doc(task._id).update({
            status: "failed",
            errorMessage: `字幕烧录失败：输出文件不存在 (HTTP ${response.status})`,
            updateTime: new Date(),
          });

          return {
            taskId: task._id,
            status: "failed",
            message: `字幕烧录失败：输出文件不存在 (HTTP ${response.status})`
          };
        }
      } catch (error) {
        console.error("❌ [字幕烧录检查] 步骤6: 验证视频文件存在性失败");
        console.error("❌ [字幕烧录检查] 错误详情：", error.message);
        console.error("❌ [字幕烧录检查] 错误类型：", error.name);

        // 如果验证失败，也标记为失败
        await tasksCollection.doc(task._id).update({
          status: "failed",
          errorMessage: `字幕烧录失败：无法验证输出文件 (${error.message})`,
          updateTime: new Date(),
        });

        return {
          taskId: task._id,
          status: "failed",
          message: `字幕烧录失败：无法验证输出文件 (${error.message})`
        };
      }
    }

    console.log("✅ [字幕烧录检查] 步骤7: 文件验证通过，准备更新任务状态");
    console.log("🎯 [字幕烧录检查] 最终视频地址：", finalVideoUrl);

    console.log("🎬 [字幕烧录检查] 步骤8: 更新任务状态为完成");
    // 更新任务状态为完成
    await tasksCollection.doc(task._id).update({
      status: "completed",
      finalVideoUrl: finalVideoUrl,
      updateTime: new Date(),
    });

    console.log("🎉 [字幕烧录检查] 步骤9: 任务状态更新完成");
    console.log("✅ [字幕烧录检查] 字幕烧录流程全部完成");

    return {
      taskId: task._id,
      status: "success",
      message: "字幕烧录完成",
      finalVideoUrl: finalVideoUrl
    };
  } else {
    console.error("无法获取最终视频文件地址");
    await tasksCollection.doc(task._id).update({
      status: "failed",
      errorMessage: "无法获取最终视频文件地址",
      updateTime: new Date(),
    });

    return {
      taskId: task._id,
      status: "failed",
      message: "无法获取最终视频文件地址"
    };
  }
}
